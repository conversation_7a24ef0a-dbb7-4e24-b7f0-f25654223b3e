/* Import Tailwind CSS */
@import url('https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css');

body {
    font-family: 'Orbitron', sans-serif;
    background-color: #0F172A; /* A darker slate/navy, Tailwind slate-900 */
    color: #67E8F9; /* Tailwind cyan-300 */
    scroll-behavior: smooth;
}

/* Cyberpunk/Tech Theme Enhancements */
.bg-gray-900 { background-color: #0F172A; } /* slate-900 */
.bg-gray-800 { background-color: #1E293B; } /* slate-800 */
.bg-gray-700 { background-color: #334155; } /* slate-700 */
.text-cyan-400 { color: #67E8F9; } /* cyan-300 */
.text-cyan-300 { color: #A5F3FC; } /* cyan-200 */
.text-purple-400 { color: #C084FC; } /* purple-400 */
.text-purple-300 { color: #D8B4FE; } /* purple-300 */
.border-cyan-500 { border-color: #22D3EE; } /* cyan-500 */
.focus\:ring-cyan-500:focus { --tw-ring-color: #22D3EE; }
.focus\:border-cyan-500:focus { border-color: #22D3EE; }


/* Header Styling */
header {
    border-bottom: 1px solid #334155; /* slate-700 */
}

.nav-link {
    position: relative;
    color: #94A3B8; /* slate-400 */
    transition: color 0.3s ease, transform 0.3s ease;
}

.nav-link:hover {
    color: #E0F2FE; /* cyan-100 */
    transform: translateY(-2px);
}

.active-nav-link {
    color: #67E8F9; /* cyan-300 */
    font-weight: 500;
    border-bottom: 2px solid #67E8F9;
}
.nav-link-mobile.active-nav-link {
    background-color: #1E3A8A; /* A darker blue for active mobile link */
    color: #E0F2FE; /* cyan-100 */
}


/* Neon Glow Effects */
.neon-glow-blue {
    box-shadow: 0 0 5px #22D3EE, 0 0 10px #22D3EE, 0 0 15px #22D3EE, 0 0 20px #22D3EE;
}
.neon-glow-blue-soft {
    filter: drop-shadow(0 0 8px #22d3ee77);
}
.neon-glow-purple {
    box-shadow: 0 0 5px #A855F7, 0 0 10px #A855F7, 0 0 15px #A855F7;
}
.neon-glow-purple-soft {
     filter: drop-shadow(0 0 8px #a855f7aa);
}

/* Button Styling */
button, .button {
    transition: all 0.3s ease-in-out;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}
button:hover, .button:hover {
    transform: translateY(-2px);
}
button:active, .button:active {
    transform: translateY(0px);
}

#connectWalletBtn {
    border-width: 2px;
    border-style: solid;
    border-image: linear-gradient(to right, #8B5CF6, #EC4899) 1; /* Purple to Pink */
    background-image: linear-gradient(to right, #7C3AED, #DB2777);
    color: white;
}
#connectWalletBtn:hover {
    background-image: linear-gradient(to right, #6D28D9, #BE185D);
    box-shadow: 0 0 15px #c026d388;
}


/* Input Fields */
input[type="text"], input[type="number"] {
    background-color: #1E293B; /* slate-800 */
    border: 1px solid #475569; /* slate-600 */
    color: #E2E8F0; /* slate-200 */
    border-radius: 0.375rem; /* rounded-md */
    padding: 0.75rem 1rem; /* p-3 */
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}
input[type="text"]:focus, input[type="number"]:focus {
    outline: none;
    border-color: #67E8F9; /* cyan-300 */
    box-shadow: 0 0 0 2px rgba(34, 211, 238, 0.5); /* ring-2 ring-cyan-500 */
}
input::placeholder {
    color: #64748B; /* slate-500 */
}

/* Modal Styling */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.8); /* slate-900 with opacity */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}
.modal.hidden {
    display: none;
}
.modal-content {
    animation: modal-fade-in 0.3s ease-out;
    border-width: 1px;
    border-style: solid;
    border-image: linear-gradient(145deg, #8B5CF6, #22D3EE, #EC4899) 1;
    box-shadow: 0 0 25px rgba(34, 211, 238, 0.3), 0 0 15px rgba(168, 85, 247, 0.3);
}

@keyframes modal-fade-in {
    from { opacity: 0; transform: translateY(-20px) scale(0.95); }
    to { opacity: 1; transform: translateY(0) scale(1); }
}

/* Custom Scrollbar for Token List */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}
.custom-scrollbar::-webkit-scrollbar-track {
    background: #1E293B; /* slate-800 */
    border-radius: 10px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #475569; /* slate-600 */
    border-radius: 10px;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #64748B; /* slate-500 */
}

/* Page transition placeholder */
.page-content {
    animation: page-fade-in 0.5s ease-out;
}
@keyframes page-fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loader animation */
.loader {
  border-top-color: #67E8F9; /* cyan-300 */
  animation: spin 1s linear infinite;
}
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Swap Component Specifics */
.swap-input-group {
    background-color: #1E293B; /* slate-800 */
    border: 1px solid #334155; /* slate-700 */
    border-radius: 0.5rem; /* rounded-lg */
    padding: 1rem; /* p-4 */
    margin-bottom: 1rem; /* mb-4 */
}
.swap-input-group:focus-within {
    border-color: #67E8F9; /* cyan-300 */
    box-shadow: 0 0 0 2px rgba(34, 211, 238, 0.3);
}
.token-select-button {
    background-color: #334155; /* slate-700 */
    color: #E0F2FE; /* cyan-100 */
    padding: 0.5rem 1rem;
    border-radius: 0.375rem; /* rounded-md */
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    font-size: 0.875rem; /* text-sm */
}
.token-select-button:hover {
    background-color: #475569; /* slate-600 */
}
.token-select-button img {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem; /* mr-2 */
    border-radius: 50%;
}
.swap-arrow-button {
    background-color: #334155; /* slate-700 */
    border: 1px solid #475569; /* slate-600 */
    color: #67E8F9; /* cyan-300 */
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0.5rem auto; /* my-2 */
    transition: all 0.2s ease;
}
.swap-arrow-button:hover {
    background-color: #475569; /* slate-600 */
    transform: rotate(180deg);
    border-color: #67E8F9;
}

.slippage-btn {
    background-color: #334155; /* slate-700 */
    color: #CBD5E1; /* slate-300 */
    padding: 0.375rem 0.75rem; /* py-1.5 px-3 */
    border-radius: 0.375rem; /* rounded-md */
    font-size: 0.75rem; /* text-xs */
}
.slippage-btn:hover, .slippage-btn.active {
    background-color: #4A044E; /* A dark purple, Tailwind purple-900 */
    color: white;
}

/* Shadow for cards/containers */
.card-shadow {
    box-shadow: 0px 0px 15px 0px rgba(34, 211, 238, 0.1), 0px 0px 5px 0px rgba(34, 211, 238, 0.05);
}
.shadow-inner-top {
    box-shadow: inset 0 4px 6px -1px rgba(0,0,0,0.2);
}

/* Ensure Orbitron font is applied if Tailwind overrides */
body, button, input, select, textarea, .font-orbitron {
    font-family: 'Orbitron', sans-serif !important;
}
